from flask import Flask, request, render_template, send_file, session
from stegano import lsb
import os
import uuid
import base64
import hashlib
import zlib  # For compression
import logging  # For better debugging
from Crypto.Cipher import AES
from captcha.image import ImageCaptcha  # Import CAPTCHA module

# Setup logging
logging.basicConfig(level=logging.DEBUG)

app = Flask(__name__)
app.secret_key = 'supersecretkey'  # Secret key for session management
UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

# Function to check valid file type
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Function to generate CAPTCHA
def generate_captcha():
    captcha_text = str(uuid.uuid4())[:6].upper()
    session['captcha'] = captcha_text  # Store CAPTCHA in session

    image = ImageCaptcha(width=280, height=90)
    captcha_path = os.path.join("static", "captcha.png")
    image.write(captcha_text, captcha_path)

    return captcha_path

# AES Encryption function with compression
def encrypt_message(message, password):
    key = hashlib.sha256(password.encode()).digest()  # Create 256-bit key from password
    cipher = AES.new(key, AES.MODE_EAX)
    nonce = cipher.nonce
    compressed_msg = zlib.compress(message.encode())  # Compress message before encryption
    ciphertext, tag = cipher.encrypt_and_digest(compressed_msg)

    return base64.b64encode(nonce + ciphertext).decode()

# AES Decryption function with decompression
def decrypt_message(encrypted_message, password):
    try:
        key = hashlib.sha256(password.encode()).digest()
        encrypted_data = base64.b64decode(encrypted_message)
        nonce, ciphertext = encrypted_data[:16], encrypted_data[16:]

        cipher = AES.new(key, AES.MODE_EAX, nonce=nonce)
        decrypted_msg = cipher.decrypt(ciphertext)
        return zlib.decompress(decrypted_msg).decode(errors='ignore')  # Decompress message
    except Exception as e:
        logging.error(f"Decryption Error: {e}")
        return None  # Return None if decryption fails

@app.route('/')
def index():
    captcha_path = generate_captcha()  # Generate new CAPTCHA on page load
    return render_template('index.html', captcha_path=captcha_path)

@app.route('/upload', methods=['POST'])
def upload():
    if 'file' not in request.files:
        return "Error: No file uploaded."

    file = request.files['file']
    if not allowed_file(file.filename):
        return "Error: Invalid file type."

    messageA = request.form.get('messageA', '')
    passwordA = request.form.get('passwordA', '')
    messageB = request.form.get('messageB', '')
    passwordB = request.form.get('passwordB', '')

    if file:
        filename = str(uuid.uuid4()) + os.path.splitext(file.filename)[1]
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)

        # Encrypt messages before hiding
        encrypted_msgA = encrypt_message(messageA, passwordA) if messageA else ""
        encrypted_msgB = encrypt_message(messageB, passwordB) if messageB else ""

        # Combine messages
        combined_message = f"A:{passwordA}|{encrypted_msgA}||B:{passwordB}|{encrypted_msgB}"

        # Hide messages inside image
        encoded_img = lsb.hide(file_path, combined_message)
        encoded_img.save(file_path)
        
        return send_file(file_path, as_attachment=True)

    return "Error: File upload failed."

@app.route('/decode', methods=['POST'])
def decode():
    if 'decodeFile' not in request.files:
        return "Error: No file uploaded."

    file = request.files['decodeFile']
    if not allowed_file(file.filename):
        return "Error: Invalid file type."

    password = request.form.get('decodePassword', '')
    entered_captcha = request.form.get('enteredCaptcha', '')

    if 'captcha' not in session:
        return "Error: CAPTCHA session expired. Refresh the page."

    security_text = session['captcha']  # Stored CAPTCHA

    if file:
        filename = str(uuid.uuid4()) + os.path.splitext(file.filename)[1]
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)

        try:
            hidden_message = lsb.reveal(file_path)
            logging.debug(f"Extracted Message: {hidden_message}")  # Debugging log
        except Exception as e:
            logging.error(f"Decoding Error: {e}")
            return "Error: No hidden message found."

        # Extract messages
        parts = hidden_message.split("||")
        messageA = messageB = None

        for part in parts:
            if part.startswith(f"A:{password}|"):
                encrypted_msgA = part.split("|", 1)[1]
                messageA = decrypt_message(encrypted_msgA, password)
            elif part.startswith(f"B:{password}|"):
                encrypted_msgB = part.split("|", 1)[1]
                messageB = decrypt_message(encrypted_msgB, password)

        # Validate CAPTCHA & return correct message
        if entered_captcha.lower() == security_text.lower() and messageA:
            return messageA
        elif entered_captcha[::-1].lower() == security_text.lower() and messageB:
            return messageB

    return "Error: Incorrect CAPTCHA or password."

if __name__ == '__main__':
    app.run(debug=True)
