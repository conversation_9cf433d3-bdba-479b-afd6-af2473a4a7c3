<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="static/styles.css">
    <title>Dual-Layer Steganography</title>

    <script>
        function showSection(section) {
            document.getElementById('encoder').classList.add('hidden');
            document.getElementById('decoder').classList.add('hidden');
            document.getElementById(section).classList.remove('hidden');
        }

        let pressTimer;  // Variable to store timer reference

        function startPress() {
            pressTimer = setTimeout(() => {
                document.getElementById("secretFields").style.display = "block";
            }, 5000);  // 5 seconds press to reveal the secret fields
        }

        function endPress() {
            clearTimeout(pressTimer);  // Cancel the reveal if released early
        }
    </script>
</head>
<body>
    <h1>Dual-Layer Steganography</h1>
    <button onclick="showSection('encoder')">Encode</button>
    <button onclick="showSection('decoder')">Decode</button>
    
    <div id="encoder" class="container">
        <h2>Encode</h2>
        <form action="/upload" method="post" enctype="multipart/form-data">
            <label>Choose a file:</label>
            <input type="file" id="file" name="file" title="Select a file to hide messages" required>
            
            <label>Message A:</label>
            <input type="text" id="messageA" name="messageA" placeholder="Enter normal message" required>
            <label>Password A:</label>
            <input type="password" id="passwordA" name="passwordA" placeholder="Enter normal password" required>

            

            <!-- Secret Message Fields (Initially Hidden) -->
            <div id="secretFields" class="hidden">
                <label>Message B:</label>
                <input type="text" id="messageB" name="messageB" placeholder="Enter secret message">
                <label>Password B:</label>
                <input type="password" id="passwordB" name="passwordB" placeholder="Enter secret password">
            </div>

            <button type="submit">Encode</button>
            <!-- Long Press Button to Reveal Secret Message Fields -->
            <div class="toggle-section">
                <button type="button" onmousedown="startPress()" onmouseup="endPress()" onmouseleave="endPress()">
                    Hold for 5 sec to Reveal Secret Fields
                </button>
            </div>
        </form>
    </div>
    
    <div id="decoder" class="container hidden">
        <h2>Decode</h2>
        <form action="/decode" method="post" enctype="multipart/form-data">
            <label>Choose a file:</label>
            <input type="file" id="decodeFile" name="decodeFile" title="Select a file to decode messages" required>            
            <label>Password:</label>
            <input type="password" id="decodePassword" name="decodePassword" placeholder="Enter password" required>
            
            <!-- CAPTCHA Image -->
            <label>CAPTCHA:</label>
            <img src="{{ captcha_path }}" alt="CAPTCHA Image">
            
            <label for="enteredCaptcha">Enter CAPTCHA:</label>
            <input type="text" id="enteredCaptcha" name="enteredCaptcha" required>

            <button type="submit">Decode</button>
        </form>
    </div>
</body>
</html>
