/* General Page Styles */
body {
    font-family: 'Arial', sans-serif;
    text-align: center;
    margin: 0;
    padding: 0;
    background-color: #f4f4f9;
}

/* Container */
.container {
    max-width: 500px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
}

.container:hover {
    transform: scale(1.02);
}

/* Headings */
h1 {
    color: #333;
    margin-top: 20px;
}

h2 {
    color: #444;
    margin-bottom: 15px;
}

/* Buttons */
button {
    background: #007BFF;
    color: white;
    border: none;
    padding: 10px 15px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease-in-out;
    margin: 10px;
}

button:hover {
    background: #0056b3;
}

/* Long Press Button Styling */
.toggle-section button {
    background: white;
    font-weight: bold;
}

.toggle-section button:hover {
    background: #ddd;
}

/* Form Inputs */
input[type="text"], input[type="password"], input[type="file"] {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border 0.3s, box-shadow 0.3s;
}

input[type="text"]:focus, input[type="password"]:focus {
    border-color: #007BFF;
    box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.5);
}

/* File Input Styling */
input[type="file"] {
    cursor: pointer;
    border: none;
    background: white;
}

/* Labels */
label {
    font-weight: bold;
    display: block;
    text-align: left;
    margin-top: 10px;
    color: #333;
}

/* Security Text */
.security-text {
    font-weight: bold;
    color: #007BFF;
    font-size: 16px;
}

/* Hidden Elements */
.hidden {
    display: none;
}

/* CAPTCHA Styling */
img {
    margin: 10px 0;
    border-radius: 5px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        max-width: 90%;
        padding: 15px;
    }
    
    button {
        width: 100%;
    }
}
